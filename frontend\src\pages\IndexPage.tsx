import { MainMap } from '@/components/map/MainMap'
import { trpc } from '@/trpc'
import { Case, GeoMeta, PointType } from '@/types'
import {
  Badge,
  Button,
  Card,
  CardBody,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalHeader,
  Pagination,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from "@heroui/react"
import { Link, useRouter, useSearch } from '@tanstack/react-router'
import { useEffect, useMemo, useRef, useState } from 'react'
import { SearchIcon } from '@/lib/svg/SearchIcon'
import { getCountryAndLocation } from '@/lib/utils/getCountryAndLocation'
import { CaseCard } from '@/components/CaseCard'
import { CaseCardSkeleton } from '@/components/CaseCardSkeleton'
import { useMediaQuery, useWindowScroll } from '@uidotdev/usehooks'
import { motion } from 'framer-motion'

import _fromIcon from '@/assets/from.png'
import { Marker, Popup } from 'react-leaflet'
import L from 'leaflet'
import { CDatePicker } from '@/components/DatePicker'
import CalendarIcon from '@/lib/svg/CalendarIcon'
import { useTranslation } from 'react-i18next'
import { pointColorByType } from '@/lib/utils/pointColorByType'
// import { useMatch } from '@tanstack/react-router'
import { useClickOutside } from '@/lib/utils/useClickOutside'
import { HeroBlock } from '@/components/HeroBlock'
import { LandingPage } from '@/components/LandingPage'
import i18next from 'i18next'

export const IndexPage = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { isOpen, onOpen, onOpenChange } = useDisclosure()
  const [activePointType, setActivePointType] = useState<PointType>('from')

  const [fromPoint, setFromPoint] = useState<GeoMeta>()
  const [toPoint, setToPoint] = useState<GeoMeta>()

  const [shadowFromPoint, setShadowFromPoint] = useState<GeoMeta>()
  const [shadowToPoint, setShadowToPoint] = useState<GeoMeta>()

  const [radius, setRadius] = useState(50)
  const [dates, setDates] = useState<Array<Date>>()
  const [shadowDates, setShadowDates] = useState<any>()

  const [isFiltersOpen, setIsFiltersOpen] = useState(false)
  const [scrollParams] = useWindowScroll()

  // const isLargeDevice = useMediaQuery('only screen and (min-width : 993px) and (max-width : 1200px)')
  const isSmallDevice = useMediaQuery('only screen and (max-width : 1020px)')

  const [highLightItem, setHighLightItem] = useState<Case>()

  const qs = useSearch({ from: '/' })
  // const route = useMatch()

  const [currentPage, setCurrentPage] = useState(qs.page || 1)
  const [limit, setLimit] = useState(3)

  const [isResultsVisible, setIsResultsVisible] = useState(false)
  const filtersOverlayRef = useRef<HTMLDivElement | null>(null)
  useClickOutside(filtersOverlayRef as any, () => setIsFiltersOpen(false))

  useEffect(() => {
    setIsResultsVisible(typeof fromPoint?.lat !== 'undefined' && typeof toPoint?.lat !== 'undefined')
    // !!(fromPoint?.address?.country && toPoint?.address?.country)

    setShadowFromPoint(fromPoint)
    setShadowToPoint(toPoint)
  }, [fromPoint, toPoint])

  useEffect(() => {
    if (shadowFromPoint && shadowToPoint) {
      window.document.title = `${getCountryAndLocation(shadowFromPoint)} - ${getCountryAndLocation(shadowToPoint)}`
    } else {
      window.document.title = `takeNpass`
    }
  }, [shadowFromPoint, shadowToPoint])

  const {
    data: caseData,
    isFetching,
    isError,
    error,
    isSuccess
    // refetch
  } = trpc.case.list.useQuery(
    {
      limit,
      page: currentPage,
      radius,
      dates: dates,
      lang: i18next.language,
      pointsGeoreverse: !fromPoint?.address?.location || !toPoint?.address?.location,
      from:
        typeof fromPoint?.lat !== 'undefined'
          ? {
              lat: fromPoint.lat,
              lon: fromPoint.lon
            }
          : undefined,
      to:
        typeof toPoint?.lat !== 'undefined'
          ? {
              lat: toPoint.lat,
              lon: toPoint.lon
            }
          : undefined
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: isResultsVisible
    }
  )

  useEffect(() => {
    currentPage !== qs.page && setCurrentPage(qs.page)

    if (qs.from && qs.to) {
      const [qsFrom, qsTo]: GeoMeta[] = [qs.from, qs.to]

      if (qsFrom?.lat != fromPoint?.lat && qsTo?.lat != toPoint?.lat && qsFrom?.lon != fromPoint?.lon && qsTo?.lon != toPoint?.lon) {
        setFromPoint(qsFrom)
        setToPoint(qsTo)
      }
    }

    if (!qs.to && !qs.from) {
      setIsResultsVisible(false)
      setToPoint(undefined)
      setFromPoint(undefined)
    }
  }, [qs])

  useEffect(() => {
    // qs?.from && setFromPoint(qs.from || undefined)
    // qs?.to && setToPoint(qs.to || undefined)
  }, [])

  const [data, setData] = useState(caseData?.cases)

  useEffect(() => {
    if (isSuccess) {
      setData(caseData.cases)

      if (caseData.pointsMeta?.from && caseData.pointsMeta?.to) {
        const { from, to } = caseData.pointsMeta

        setShadowFromPoint(from)
        setShadowToPoint(to)
      }
    }
  }, [isSuccess, caseData])
  // const data = useState<CaseWithRelations[]>(caseData?.cases)

  function expandSearchRaduisHandler() {
    setRadius((radius) => (radius + 100 >= 300 ? 50 : radius + 100))
  }

  function searchModalHendler(pointType: PointType) {
    setActivePointType(pointType)
    setIsFiltersOpen(false)
    onOpen()
  }

  function onSearchInputChange(data: GeoMeta) {
    if (activePointType === 'from') setFromPoint(data)
    if (activePointType === 'to') setToPoint(data)
  }

  function mapLocationValue() {
    return activePointType == 'from' ? fromPoint : toPoint
  }

  function pointClickHandler(c: Case) {
    setHighLightItem(c)
    document.querySelector('.case-' + c.id)?.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' })
  }

  function routeSubscribe() {
    router.navigate({
      to: '/create',
      search: {
        from: {
          lat: fromPoint?.lat,
          lon: fromPoint?.lon,
          type: fromPoint?.type,
          address: {
            country: fromPoint?.address.country,
            location: fromPoint?.address.location
          }
        },
        to: {
          lat: toPoint?.lat,
          lon: toPoint?.lon,
          type: toPoint?.type,
          address: {
            country: toPoint?.address.country,
            location: toPoint?.address.location
          }
        },
        isRequest: true
      }
    })
  }

  const SubscribeButton = () =>
    useMemo(
      () => (
        <Button onClick={routeSubscribe} variant='faded' color='primary'>
          {t('Subscribe to this route')}
        </Button>
      ),
      []
    )

  const CaseDatePicker = () => (
    <CDatePicker
      calendarProps={{
        selectionMode: 'range',
        value: shadowDates,
        onChange: (value: any) => changeDateHandler(value),
        onClearButtonClick: onClearDates
      }}
      inputProps={{
        startContent: (
          <div className='flex items-center'>
            <div className='w-3 h-3 bg-default-500 rounded-t-xl rounded-b-sm mr-2'></div>
            <span className='text-sm'>{t('When')}</span>
          </div>
        ),
        endContent: <CalendarIcon className='w-6 h-6' />,
        classNames: {
          input: 'text-center text-sm'
        },
        size: 'md',
        variant: 'flat',
        className: 'font-semibold sm:max-w-[340px]',
        color: 'default'
      }}
    />
  )
  const SearchBar = () =>
    useMemo(
      () => (
        <div className='w-full xl:w-auto'>
          <div className='space-y-3 xl:space-x-3 xl:space-y-0 xl:flex items-center'>
            <div onClick={() => searchModalHendler('from')}>
              <Input
                aria-label={t('Departure location')}
                startContent={
                  <div className='flex items-center'>
                    <div className='w-3 h-3 bg-primary-500 rounded-full mr-2'></div>
                    <span className='text-sm'>{t('From')}</span>
                  </div>
                }
                classNames={
                  {
                    // input: 'text-center'
                  }
                }
                endContent={<SearchIcon className='w-6 h-6' />}
                variant='flat'
                size='md'
                className='font-semibold'
                color='default'
                value={getCountryAndLocation(shadowFromPoint)}
                // onClick={() => searchModalHendler('from')}
              />
            </div>
            <div onClick={() => searchModalHendler('to')}>
              <Input
                aria-label={t('Destination location')}
                startContent={
                  <div className='flex items-center'>
                    <div className='w-3 h-3 bg-secondary-500 rounded-full mr-2'></div>
                    <span className='text-sm'>{t('To')}</span>
                  </div>
                }
                endContent={<SearchIcon className='w-6 h-6' />}
                variant='flat'
                size='md'
                className='font-semibold w-full'
                classNames={
                  {
                    // input: 'text-center'
                  }
                }
                color='default'
                value={getCountryAndLocation(shadowToPoint)}
              />
            </div>
          </div>
        </div>
      ),
      [shadowFromPoint, shadowToPoint]
    )

  function pageChangeHandler(page: number) {
    setCurrentPage(page)
  }

  function changeDateHandler(value: any) {
    setShadowDates(value)
    if (value?.start && value?.end) {
      setDates([value.start, value.end])
    }
  }

  function onClearDates() {
    setShadowDates(undefined)
    setDates(undefined)
  }

  function updateUrl() {
    router.navigate({
      to: '/',
      search: {
        page: currentPage,
        from: {
          lat: fromPoint?.lat,
          lon: fromPoint?.lon
          //// type: fromPoint?.type,
          // display_name: fromPoint?.display_name,
          // address: {
          //   country: fromPoint?.address.country,
          //   location: fromPoint?.address.location
          // }
        },
        to: {
          lat: toPoint?.lat,
          lon: toPoint?.lon
          //// type: toPoint?.type,
          // display_name: toPoint?.display_name,
          // address: {
          //   country: toPoint?.address.country,
          //   location: toPoint?.address.location
          // }
        }
      },
      startTransition: true,
      from: '/',
      hash: true,
      // mask: '',
      resetScroll: true
      // replace: true
    })
  }

  useEffect(() => {
    // updateUrl()
    if (typeof fromPoint?.lat !== 'undefined' && typeof toPoint?.lat !== 'undefined') {
      updateUrl()
      // console.log({ currentPage, fromPoint, toPoint })
    }
  }, [currentPage, fromPoint, toPoint])

  return (
    <>
      {/* isResultsVisible: {isResultsVisible && 'da' || 'net'} */}
      {scrollParams?.y > 300 && isSmallDevice && (
        <motion.div initial={{ opacity: 0, y: -30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} className='fixed right-2 top-20 z-10'>
          <Popover
            shouldBlockScroll
            showArrow
            backdrop='blur'
            // disableAnimation={true}
            placement='bottom-start'
            isOpen={isFiltersOpen}
            onOpenChange={(open) => setIsFiltersOpen(open)}
          >
            <PopoverTrigger>
              <div></div>
            </PopoverTrigger>
            <PopoverContent>
              <div ref={filtersOverlayRef} className='my-3 flex flex-col items-end lg:flex-row gap-3 justify-end xl:justify-center flex-wrap'>
                <SearchBar />
                <CaseDatePicker />
              </div>
            </PopoverContent>
          </Popover>

          <Badge onClick={() => setIsFiltersOpen(true)} content='2' color='danger'>
            <Button isIconOnly onClick={() => setIsFiltersOpen(true)} radius='md' variant='shadow' color='primary'>
              <SearchIcon className='w-6 h-6' />
            </Button>
          </Badge>
        </motion.div>
      )}

      <div className='relative lg:container mx-auto justify-center gap-10'>
        <div className='2nd-block transition-width duration-150 ease-in-out _lg:w-2/5 _xl:w-3/6 h-auto'>
          <div className='mb-7'>
            <HeroBlock />
            <div className='flex flex-col items-end lg:flex-row gap-3 justify-end xl:justify-center flex-wrap'>
              <SearchBar />
              <div>
                <CaseDatePicker />
              </div>
            </div>
          </div>
          {!isSmallDevice && isResultsVisible && (
            <MainMap mapStyles={{ height: '300px' }} readOnly initBrowserGeocode={false} initZoom={3} mapClassName='z-0 rounded-lg'>
              {data?.map((c) => (
                <div key={c.id}>
                  {[{ ...c.from, type: 'from' }, ...c.middlepoints.map((p) => ({ ...p, type: 'middlepoints' })), { ...c.to, type: 'to' }].map((p) => (
                    <Marker
                      key={p.lat}
                      // icon={L.icon({
                      //   iconUrl: _fromIcon,
                      //   iconSize: [30, 30]
                      // })}
                      icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType(p.type)} rounded-full` })}
                      position={[p.lat, p.lon]}
                    >
                      <Popup>
                        <div className='dark:brightness-0 dark:contrast-0 dark:hue-rotate-0 dark:invert-0 dark:saturate-0'>
                          <div className='text-sm'>{p.geometa.display_name}</div>
                          <div className='flex justify-end'>
                            <Button radius='sm' onClick={() => pointClickHandler(c)} color='primary' variant='flat'>
                              {t('Show')}
                            </Button>
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}
                  {/* <Polyline color='gray' positions={[c?.from, ...(c?.middlepoints ?? []), c?.to].map((point) => [point?.lat, point?.lon])} /> */}
                </div>
              ))}
            </MainMap>
          )}
        </div>

        {isResultsVisible && (
          <div className='1nd-block transition-width duration-150 ease-in-out space-y-10 mt-10 shrink grow'>
            {fromPoint && toPoint && data?.length > 0 && (
              <div className='lg:mt-5 w-full flex justify-end'>
                <SubscribeButton />
              </div>
            )}
            {isFetching &&
              Array.from({ length: limit }).map((i, index) => (
                <Card key={index} shadow='lg'>
                  <CardBody>
                    <CaseCardSkeleton key={index} />
                  </CardBody>
                </Card>
              ))}

            {data?.length == 0 && (
              <Card isFooterBlurred shadow='none' className='bborder-2 border-danger-300' classNames={{ body: 'p-2 py-4' }}>
                <CardBody>
                  <div className='text-center text-danger-400 font-semibold'>{t('No results found')}</div>
                  <div className='flex justify-center space-x-5 mt-5'>
                    {radius < 300 && (
                      <Button onClick={expandSearchRaduisHandler} variant='flat' color='primary'>
                        {t('Expand radius')} ({radius + 50} km)
                      </Button>
                    )}
                    <SubscribeButton />
                  </div>
                </CardBody>
              </Card>
            )}

            {!isFetching && (
              <ul className='space-y-10'>
                {data?.map((c) => (
                  <li key={c.id} className='item transition-transform duration-150 hover:translate-y-1'>
                    <Card
                      key={c.id}
                      className={'case-' + c.id}
                      classNames={{
                        body: `p-3`,
                        base: `${highLightItem?.id == c.id && 'border-4 border-warning-400'}`
                      }}
                      shadow='lg'
                    >
                      <CardBody>
                        <Link to='/route/$caseId' params={{ caseId: c.id }}>
                          <CaseCard fromPoint={fromPoint} toPoint={toPoint} key={c.id} c={c} favBtn showStatus />
                        </Link>
                      </CardBody>
                    </Card>
                  </li>
                ))}
              </ul>
            )}

            {!isFetching && data?.length > 0 && (
              <div className='flex justify-end'>
                <Pagination loop page={currentPage} onChange={pageChangeHandler} initialPage={currentPage} showControls total={caseData?.pages || 1} />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Landing Page Section */}
      <LandingPage />

      <Modal
        classNames={{
          base: 'max-w-xl!'
        }}
        backdrop='blur'
        placement='bottom-center'
        isOpen={isOpen}
        onOpenChange={onOpenChange}
      >
        <ModalContent className='z-20'>
          {(onClose) => (
            <>
              <ModalHeader className='flex flex-col gap-1'>{t('Find location')}</ModalHeader>
              <ModalBody>
                <div>
                  <MainMap
                    key={activePointType}
                    // mapStyles={{ height: '320px' }}
                    mapClassName='h-[320px]! lg:h-[460px]!'
                    // initBrowserGeocode={true}
                    initBrowserGeocode={false}
                    showSearchInput
                    onChangeLocation={onSearchInputChange}
                    // readOnly
                    // initGeoReverse={activePointType === 'from'}
                    initGeoReverse={false}
                    initZoom={2}
                    initLocationValue={mapLocationValue()}
                    // setMapInstance={setMap}
                    showFindMe
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color='primary' variant='flat' onPress={onClose}>
                  {t('Close')}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
