import { useUserStore } from '@/store'
import { useEffect } from 'react'
import { trpc } from '@/trpc'

interface Props {
  setLoading?: (isFetching: boolean) => void
}

export default function ValidateUser({ setLoading }: Props) {
  const userStore = useUserStore()

  // Используем tRPC для валидации сессии
  const { data: userData, isFetching, isError } = trpc.users.validate.useQuery(undefined, {
    retry: false,
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    if (userData) {
      userStore.setData(userData as any)
    } else if (isError) {
      userStore.setData(null)
    }
  }, [userData, isError])

  useEffect(() => {
    setLoading && setLoading(isFetching)
    userStore.setIsFetching(isFetching)
  }, [isFetching])

  return <></>
}
