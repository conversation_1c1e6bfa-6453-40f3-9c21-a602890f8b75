import IconSendPlaneFill from '@/lib/svg/SendIcon'
import { trpc } from '@/trpc'
import { User as UserType } from '@/types'
import { Avatar, Button, Card, CardBody, CardFooter, CardHeader, Input, ScrollShadow, Snippet, User } from "@heroui/react"
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { MessageSkeleton } from './MessageSkeleton'
// import { SocketNotification } from '../../../../server/src/types/SocketNotificationType'
// import { socket } from '@/socket'
import useBus from 'use-bus'
import { Link } from '@tanstack/react-router'
import { toast } from 'react-toastify'
import { useIntersectionObserver } from '@uidotdev/usehooks'
import { useUserStore } from '@/store'

interface Props {
  activeUser?: UserType
}

export const MainChat = ({ activeUser }: Props) => {
  const { t } = useTranslation()
  const [inputValue, setInputValue] = useState('')
  const { data: sessionUser } = useUserStore()

  const { data: sendData, isPending: send_isLoading, isError: send_isError, isSuccess: send_isSuccess, mutateAsync: sendMessageReq } = trpc.message.send.useMutation()
  const { data, isError, isFetching, isLoading, isSuccess, refetch } = trpc.message.messagesByPartner.useQuery(activeUser?.id, {
    enabled: !!activeUser?.id
  })

  const sendNodeElement = useRef<HTMLDivElement>(null)
  const chatNodeElement = useRef<HTMLDivElement>(null)

  useBus('NEW_MESSAGE', () => {
    refetch()
  })

  async function sendMessageHandler() {
    // Валидация входных данных
    if (!inputValue.trim().length) {
      toast.error(t('Please enter a message'))
      return false
    }

    if (!activeUser?.id) {
      toast.error(t('Please select a recipient'))
      return false
    }

    await sendMessageReq({
      content: inputValue.trim(),
      receiverId: activeUser.id
    })

    // TODO: replace refetch by key
    await refetch()
  }

  function scrollToBottom() {
    if (chatNodeElement.current) {
      const chatDiv = chatNodeElement.current
      chatDiv.scrollTo({
        top: chatDiv.scrollHeight + 300,
        behavior: 'smooth'
      })
    }
  }

  const MessageItem = ({ item }: { item: (typeof data)[0] }) => {
    const elementRef = useRef(null)
    const visible = useIntersectionObserver(elementRef)

    const { mutateAsync } = trpc.message.setAsRead.useMutation()

    useEffect(() => {
      visible &&
        !item.read &&
        item.senderId !== sessionUser?.id &&
        mutateAsync({
          messageId: item.id
        })
    }, [visible])

    return (
      <Snippet
        ref={elementRef}
        color={item.sender?.id == activeUser?.id ? 'default' : 'primary'}
        hideSymbol
        disableTooltip={false}
        tooltipProps={{
          content: t('Copy to clipboard')
        }}
        size='md'
      >
        {item.content}
      </Snippet>
    )
  }

  useEffect(() => {
    if (send_isSuccess && !send_isLoading) {
      setInputValue('')
      setTimeout(() => {
        scrollToBottom()
      }, 500)
    }
  }, [send_isLoading, send_isSuccess])

  useEffect(() => {
    if (isSuccess && !isLoading) {
      setTimeout(() => {
        scrollToBottom()
      }, 500)
    }
  }, [isLoading, isSuccess, data])

  useEffect(() => {
    if (send_isError) {
      toast.error(t('Something went wrong, please try again later'), {
        className: 'bg-danger-50 text-danger-900 font-semibold text-sm',
        autoClose: 6000
      })
    }
  }, [send_isError])

  return (
    <>
      <Card shadow='lg'>
        <CardBody>
          <div className='flex justify-end border-b pb-2 border-default-300'>
            {/* <div>{activeUser?.username || ''}</div> */}
            {/* <Button onClick={() => scrollToBottom()}>scroll</Button> */}
            {activeUser && (
              <Link to='/profile' search={{ userId: activeUser.id }}>
                <User
                  className='text-ellipsis'
                  classNames={{
                    name: 'text-default-900 text-ellipsis text-base'
                  }}
                  name={activeUser?.username}
                  //   description={<div>{partner.email}</div>}
                  avatarProps={{
                    size: 'md',
                    color: 'default',
                    src: activeUser?.avatar?.base64string
                  }}
                />
              </Link>
            )}
          </div>
          <div className='space-y-7'>
            {isLoading && activeUser?.id && (
              <div>
                <div className='space-y-3'>
                  {[...Array(3)].map((i, index) => (
                    <div key={index} className={'flex ' + (index % 2 === 0 ? 'justify-start' : 'justify-end')}>
                      <MessageSkeleton />
                    </div>
                  ))}
                </div>
              </div>
            )}

            <ScrollShadow hideScrollBar offset={0} ref={chatNodeElement} visibility='auto' className='relative h-[50vh]'>
              {data?.map((item) => (
                <div key={item?.id} className={'flex ' + (item.sender.id != activeUser?.id ? 'justify-end' : 'justify-start')}>
                  <div className='mb-5'>
                    <div className='text-default-500 text-right ffont-semibold text-xs mb-3'>{item.createdAt.toLocaleString()}</div>
                    <div className='space-y-3 md:space-x-3 md:space-y-0 md:flex flex-wrap items-center'>
                      <Avatar color={item.sender.id == activeUser?.id ? 'default' : 'primary'} name={item.sender.username} src={item.sender?.avatar?.base64string} />
                      <div>{item && <MessageItem item={item} />}</div>
                    </div>
                  </div>
                </div>
              ))}
            </ScrollShadow>

            {data?.length == 0 && <div className='text-center text-default-600'>{t('No messages')}</div>}
            {!activeUser?.id && <div className='text-center text-default-600'>{t('Choose user')}</div>}
          </div>
        </CardBody>
        <CardFooter>
          <div ref={sendNodeElement} className='flex justify-end w-full space-x-3'>
            <Input
              aria-label={t('Type your message')}
              classNames={{
                input: 'w-full'
              }}
              onKeyDown={(e) => e.key === 'Enter' && sendMessageHandler()}
              value={inputValue}
              onValueChange={setInputValue}
              size='sm'
              className='w-full'
              placeholder={activeUser?.id ? t('Input text here') : t('Select a user to start messaging')}
              isDisabled={!activeUser?.id}
            />
            <Button
              isLoading={send_isLoading}
              onPress={sendMessageHandler}
              size='lg'
              isIconOnly
              isDisabled={!activeUser?.id || !inputValue.trim().length}
              aria-label={t('Send message')}
            >
              <IconSendPlaneFill className='w-7 h-7' />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </>
  )
}
