import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { User } from '@/lib/auth'

interface UserState {
  data: User | null
  setData: (data: User | null) => void
  isFetching: boolean
  setIsFetching: (value: boolean) => void
}

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set) => ({
        data: null,
        isFetching: false,
        setData: (data: User | null) => set((state) => ({ data })),
        setIsFetching: (value: boolean) => set((state) => ({ isFetching: value }))
      }),
      {
        name: 'user-storage'
      }
    )
  )
)
