import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// import qs from 'query-string'
import { StrictMode } from 'react'
import { RouterProvider, Router, Route, RootRoute } from '@tanstack/react-router'
import { ProfilePage } from './pages/ProfilePage.tsx'
import { CreateCasePage } from './pages/CreateCasePage.tsx'
import { IndexPage } from './pages/IndexPage.tsx'
import { CasePage } from './pages/CasePage.tsx'
import { MyCasesPage } from './pages/MyCases.tsx'
import { RegPage } from './pages/RegPage.tsx'
import { GeoMeta } from './types.ts'

import './i18n.ts'
import { RatingPage } from './pages/RatingsPage.tsx'
import { SettingsPage } from './pages/SettingsPage.tsx'
import { LoginPage } from './pages/LoginPage.tsx'
import { ChatPage } from './pages/ChatPage.tsx'

interface CaseSearch {
  page?: number
  from?: GeoMeta | undefined
  to?: GeoMeta | undefined
  radius?: number
  // filter: string
  // sort: 'newest' | 'oldest' | 'price'
}

interface CreateSearch {
  from?: GeoMeta
  to?: GeoMeta
  isRequest?: boolean
}

interface EditSearch {
  caseId: string
}



interface MessageSearch {
  partnerId?: string
}

interface ProfileSearch {
  userId?: string
}

// Create a root route
const rootRoute = new RootRoute({
  component: App
})

// const profileRootRoute = new Route({
//   getParentRoute: () => rootRoute,
//   path: '/profile',
//   component: App
// })

const indexRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/',
  component: IndexPage,
  // key: ({params, search}) => search,
  validateSearch: (search: Record<string, unknown>): CaseSearch => {
    // console.log("🚀 ~ file: main.tsx:30 ~ search:", search)
    // validate and parse the search params into a typed state
    return {
      page: Number(search?.page || 1)
      // filter: search.filter || '',
      // sort: search.sort || 'newest',
    }
  }
  // loader: ({ search }) => {
  //   search
  // },
})

// const startRoute = new Route({
//   getParentRoute: () => rootRoute,
//   path: '/reg',
//   component: RegPage
// })

const regRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/reg',
  component: RegPage
})

const loginRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage
})

const settingsRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/settings',
  component: SettingsPage
})

const profileRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/profile',
  validateSearch: (search: Record<string, unknown>): ProfileSearch => {
    return search
  },
  component: ProfilePage
})

const chatRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/messages',
  validateSearch: (search: Record<string, unknown>): MessageSearch => {
    return search
  },
  component: ChatPage
})

const createCaseRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/create',
  validateSearch: (search: Record<string, unknown>): CreateSearch => {
    return search
  },
  component: CreateCasePage
})

const editCaseRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/edit',
  validateSearch: (search: Record<string, unknown>): EditSearch => {
    return search
  },
  component: CreateCasePage
})

const caseRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/route/$caseId',
  component: CasePage
})

const myCasesRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/favorites',
  component: MyCasesPage
})

const ratingsRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/reviews',
  component: RatingPage
})

// Create the route tree using your routes
const routeTree = rootRoute.addChildren([indexRoute, profileRoute, settingsRoute, createCaseRoute, editCaseRoute, caseRoute, myCasesRoute, regRoute, loginRoute, ratingsRoute, chatRoute])

// function customStringifier(searchObj: unknown) {
//   const u = new URLSearchParams()
//   let s
//   try {
//     s = JSON.stringify(searchObj)
//   } catch (error) {}

//   try {
//     const v = Object.keys(searchObj || {}).length ? window.btoa(encodeURIComponent(JSON.stringify(searchObj))) : ''

//     u.set('s', v)

//     return v ? '?' + u.toString() : ''
//     // return v ? '?s=' + v : '' //
//   } catch (error) {
//     return ''
//   }
// }

// function customParser(searchString: string) {
//   const u = new URLSearchParams(searchString)
//   let jp

//   try {
//     const ev = u.get('s')
//     // const dv = ev ? window.atob(ev) : ''
//     const dv = ev ? window.atob(ev) : ''

//     jp = JSON.parse(decodeURIComponent(dv))
//   } catch (error) {}

//   return jp
// }

// Create the router using your route tree
const router = new Router({
  //format
  routeTree,
  // stringifySearch: customStringifier,
  // parseSearch: customParser
})

// Register your router for maximum type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

// Render our app!
const rootElement = document.getElementById('app')!
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <RouterProvider router={router} />
    </StrictMode>
  )
}
