import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { $prisma } from "./prisma";

export const auth = betterAuth({
  database: prismaAdapter($prisma, {
    provider: "mysql",
  }),
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3003",
  secret: process.env.BETTER_AUTH_SECRET || "your-secret-key-here-change-in-production",
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Отключаем для упрощения
  },
  session: {
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24, // 24 часа
    },
    expiresIn: 60 * 60 * 24 * 7, // 7 дней
    updateAge: 60 * 60 * 24, // обновлять каждый день
  },
  trustedOrigins: ["http://localhost:5173", "http://localhost:3000", "http://localhost:3003"],
  advanced: {
    database: {
      generateId: () => crypto.randomUUID(),
    },
  },
});

type SessionContext = typeof auth.$Infer.Session;
export type Session = SessionContext["session"];
export type User = SessionContext["user"];
